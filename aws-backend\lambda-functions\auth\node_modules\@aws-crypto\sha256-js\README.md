# crypto-sha256-js

A pure JS implementation SHA256.

## Usage

- To hash "some data"
```
import {Sha256} from '@aws-crypto/sha256-js';

const hash = new Sha256();
hash.update('some data');
const result = await hash.digest();

```

- To hmac "some data" with "a key"
```
import {Sha256} from '@aws-crypto/sha256-js';

const hash = new Sha256('a key');
hash.update('some data');
const result = await hash.digest();

```

## Test

`npm test`
