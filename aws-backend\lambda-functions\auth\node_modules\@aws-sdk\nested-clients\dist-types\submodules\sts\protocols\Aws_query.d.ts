import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import { AssumeRoleCommandInput, AssumeRoleCommandOutput } from "../commands/AssumeRoleCommand";
import { AssumeRoleWithWebIdentityCommandInput, AssumeRoleWithWebIdentityCommandOutput } from "../commands/AssumeRoleWithWebIdentityCommand";
/**
 * serializeAws_queryAssumeRoleCommand
 */
export declare const se_AssumeRoleCommand: (input: AssumeRoleCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_queryAssumeRoleWithWebIdentityCommand
 */
export declare const se_AssumeRoleWithWebIdentityCommand: (input: AssumeRoleWithWebIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_queryAssumeRoleCommand
 */
export declare const de_AssumeRoleCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssumeRoleCommandOutput>;
/**
 * deserializeAws_queryAssumeRoleWithWebIdentityCommand
 */
export declare const de_AssumeRoleWithWebIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssumeRoleWithWebIdentityCommandOutput>;
